Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 10846
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/clients/ HTTP/1.1" 200 52
"POST /api/clients/ HTTP/1.1" 201 240
"GET /api/projects/ HTTP/1.1" 200 52
"GET /api/tasks/ HTTP/1.1" 200 52
Watching for file changes with StatReloader
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
Internal Server Error: /api/auth/profile/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/authentication/views.py", line 55, in user_profile
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
           ^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: It is redundant to specify `source='full_name'` on field 'CharField' in serializer 'UserProfileSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/auth/profile/ HTTP/1.1" 500 138384
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/clients/ HTTP/1.1" 201 237
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/clients/ HTTP/1.1" 201 226
"POST /api/auth/login/ HTTP/1.1" 200 950
Bad Request: /api/projects/
"POST /api/projects/ HTTP/1.1" 400 42
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/projects/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: projects_project.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/views.py", line 61, in perform_create
    serializer.save(project_manager=self.request.user)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 212, in save
    self.instance = self.create(validated_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/serializers.py", line 98, in create
    project = Project.objects.create(**validated_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: projects_project.client_id
"POST /api/projects/ HTTP/1.1" 500 226516
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/clients/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/mixins.py", line 43, in list
    return self.get_paginated_response(serializer.data)
                                       ^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 768, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 686, in to_representation
    return [
           ^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
           ^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: It is redundant to specify `source='mood_emoji'` on field 'CharField' in serializer 'ClientListSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/clients/ HTTP/1.1" 500 146227
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/clients/serializers.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/clients/serializers.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/clients/serializers.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/clients/ HTTP/1.1" 200 1193
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/projects/ HTTP/1.1" 200 52
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/projects/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: projects_project.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/views.py", line 61, in perform_create
    serializer.save(project_manager=self.request.user)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 212, in save
    self.instance = self.create(validated_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/serializers.py", line 98, in create
    project = Project.objects.create(**validated_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: projects_project.client_id
"POST /api/projects/ HTTP/1.1" 500 226516
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/projects/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.IntegrityError: NOT NULL constraint failed: projects_project.client_id

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/mixins.py", line 19, in create
    self.perform_create(serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/views.py", line 61, in perform_create
    serializer.save(project_manager=self.request.user)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 212, in save
    self.instance = self.create(validated_data)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/serializers.py", line 98, in create
    project = Project.objects.create(**validated_data)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 814, in save
    self.save_base(
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 877, in save_base
    updated = self._save_table(
              ^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1020, in _save_table
    results = self._do_insert(
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/base.py", line 1061, in _do_insert
    return manager._insert(
           ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/db/backends/sqlite3/base.py", line 328, in execute
    return super().execute(query, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.IntegrityError: NOT NULL constraint failed: projects_project.client_id
"POST /api/projects/ HTTP/1.1" 500 226516
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/projects/serializers.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/projects/ HTTP/1.1" 201 417
"POST /api/auth/login/ HTTP/1.1" 200 950
Bad Request: /api/tasks/
"POST /api/tasks/ HTTP/1.1" 400 85
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/tasks/ HTTP/1.1" 201 352
/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/tasks/serializers.py changed, reloading.
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/tasks/ HTTP/1.1" 201 340
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"GET /api/clients/ HTTP/1.1" 200 1193
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"GET /api/projects/ HTTP/1.1" 200 549
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/tasks/ HTTP/1.1" 200 947
Internal Server Error: /api/auth/profile/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/authentication/views.py", line 55, in user_profile
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
           ^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: It is redundant to specify `source='full_name'` on field 'CharField' in serializer 'UserProfileSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/auth/profile/ HTTP/1.1" 500 138384
"POST /api/auth/login/ HTTP/1.1" 200 950
Internal Server Error: /api/auth/profile/
Traceback (most recent call last):
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/authentication/views.py", line 55, in user_profile
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/Sites/1sterp_laravel_nextjs/apps/backend/venv/lib/python3.11/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
           ^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: It is redundant to specify `source='full_name'` on field 'CharField' in serializer 'UserProfileSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/auth/profile/ HTTP/1.1" 500 138384
"POST /api/auth/login/ HTTP/1.1" 200 950
Watching for file changes with StatReloader
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 286
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 286
Unauthorized: /api/tasks/
"GET /api/tasks/ HTTP/1.1" 401 286
Unauthorized: /api/tasks/
"GET /api/tasks/ HTTP/1.1" 401 286
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 286
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4350
"GET /static/admin/css/rtl.css HTTP/1.1" 200 4918
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/login.css HTTP/1.1" 200 958
"GET /static/admin/css/base.css HTTP/1.1" 200 21310
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/responsive_rtl.css HTTP/1.1" 200 1864
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2661
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/projects/ HTTP/1.1" 200 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
i/clients/ HTTP/1.1" 200 1193
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/mtbrmg_erp/settings.py changed, reloading.
/app/mtbrmg_erp/settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4350
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2661
"POST /admin/login/?next=/admin/ HTTP/1.1" 200 4599
"GET /static/admin/css/base.css HTTP/1.1" 304 0
"GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive_rtl.css HTTP/1.1" 304 0
"GET /static/admin/css/login.css HTTP/1.1" 304 0
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
"GET /static/admin/js/theme.js HTTP/1.1" 304 0
"GET /static/admin/css/rtl.css HTTP/1.1" 304 0
"GET /static/admin/css/responsive.css HTTP/1.1" 304 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 950
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/clients/ HTTP/1.1" 200 0
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
"OPTIONS /api/projects/ HTTP/1.1" 200 0
Unauthorized: /api/projects/
"GET /api/projects/ HTTP/1.1" 401 286
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/clients/
"GET /api/clients/ HTTP/1.1" 401 286
"GET /admin/ HTTP/1.1" 302 0
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/projects/ HTTP/1.1" 200 52
"GET /admin/ HTTP/1.1" 302 0
"OPTIONS /api/tasks/ HTTP/1.1" 200 0
"GET /api/tasks/ HTTP/1.1" 200 52
"GET /api/projects/ HTTP/1.1" 200 52
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/clients/ HTTP/1.1" 200 1193
Watching for file changes with StatReloader
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/clients/ HTTP/1.1" 200 1193
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/clients/ HTTP/1.1" 200 1193
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
"OPTIONS /api/clients/3/ HTTP/1.1" 200 0
"PATCH /api/clients/3/ HTTP/1.1" 200 778
"GET /api/projects/ HTTP/1.1" 200 549
"GET /api/clients/ HTTP/1.1" 200 1195
"POST /api/clients/ HTTP/1.1" 201 281
Internal Server Error: /api/auth/profile/
Traceback (most recent call last):
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/views/decorators/csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/views.py", line 55, in user_profile
    return Response(serializer.data)
                    ^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 555, in data
    ret = super().data
          ^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 507, in to_representation
    for field in fields:
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 368, in _readable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/django/utils/functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/serializers.py", line 357, in fields
    fields[key] = value
    ~~~~~~^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/utils/serializer_helpers.py", line 169, in __setitem__
    field.bind(field_name=key, parent=self.serializer)
  File "/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework/fields.py", line 367, in bind
    assert self.source != field_name, (
AssertionError: It is redundant to specify `source='full_name'` on field 'CharField' in serializer 'UserProfileSerializer', because it is the same as the field name. Remove the `source` keyword argument.
"GET /api/auth/profile/ HTTP/1.1" 500 142633
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/serializers.py changed, reloading.
Watching for file changes with StatReloader
/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/authentication/serializers.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 1089
"POST /api/clients/ HTTP/1.1" 201 273
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/clients/ HTTP/1.1" 200 1942
"GET /api/auth/profile/ HTTP/1.1" 200 363
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/clients/ HTTP/1.1" 200 1942
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/clients/ HTTP/1.1" 200 1942
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 950
"GET /api/projects/ HTTP/1.1" 200 549
"GET /api/tasks/ HTTP/1.1" 200 947
"GET /api/projects/ HTTP/1.1" 200 549
Bad Request: /api/projects/
"POST /api/projects/ HTTP/1.1" 400 142
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1089
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
"POST /api/clients/ HTTP/1.1" 201 194
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 72
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 75
Bad Request: /api/clients/
"POST /api/clients/ HTTP/1.1" 400 101
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/logout/ HTTP/1.1" 200 53
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/auth/logout/
"POST /api/auth/logout/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 1089
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 1089
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"POST /api/auth/login/ HTTP/1.1" 200 1089
"OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
"POST /api/auth/logout/ HTTP/1.1" 200 53
"POST /api/auth/login/ HTTP/1.1" 200 1089
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
auth/profile/ HTTP/1.1" 200 363
"GET /api/auth/profile/ HTTP/1.1" 200 363
Watching for file changes with StatReloader
