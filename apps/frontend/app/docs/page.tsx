'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  BookOpen,
  Code,
  Database,
  Settings,
  Shield,
  Zap,
  Download,
  ExternalLink,
  ChevronRight,
  FileText,
  Terminal,
  Globe
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';

export default function DocsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل الوثائق...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  const documentationSections = [
    {
      title: 'دليل المستخدم',
      description: 'دليل شامل لاستخدام جميع ميزات النظام',
      icon: BookOpen,
      badge: 'مكتمل',
      badgeVariant: 'default' as const,
      items: [
        'مقدمة عن النظام',
        'إعداد الحساب الأول',
        'استخدام لوحة التحكم',
        'إدارة العملاء والمشاريع',
        'التقارير والإحصائيات',
        'الإعدادات المتقدمة'
      ]
    },
    {
      title: 'دليل المطور',
      description: 'وثائق تقنية للمطورين والمهندسين',
      icon: Code,
      badge: 'قيد التطوير',
      badgeVariant: 'secondary' as const,
      items: [
        'هيكل المشروع',
        'API Documentation',
        'قاعدة البيانات',
        'المصادقة والأمان',
        'نشر التطبيق',
        'استكشاف الأخطاء'
      ]
    },
    {
      title: 'دليل الإدارة',
      description: 'إدارة النظام والمستخدمين',
      icon: Settings,
      badge: 'جديد',
      badgeVariant: 'destructive' as const,
      items: [
        'إدارة المستخدمين',
        'الأدوار والصلاحيات',
        'إعدادات النظام',
        'النسخ الاحتياطي',
        'مراقبة الأداء',
        'الأمان والحماية'
      ]
    },
    {
      title: 'دليل الأمان',
      description: 'أفضل الممارسات الأمنية',
      icon: Shield,
      badge: 'مهم',
      badgeVariant: 'outline' as const,
      items: [
        'سياسات كلمات المرور',
        'المصادقة الثنائية',
        'تشفير البيانات',
        'مراجعة السجلات',
        'إدارة الجلسات',
        'الامتثال للمعايير'
      ]
    }
  ];

  const quickLinks = [
    {
      title: 'API Reference',
      description: 'مرجع شامل لجميع نقاط API',
      icon: Terminal,
      url: '#'
    },
    {
      title: 'قاعدة البيانات',
      description: 'مخطط قاعدة البيانات والجداول',
      icon: Database,
      url: '#'
    },
    {
      title: 'دليل التثبيت',
      description: 'خطوات تثبيت النظام',
      icon: Download,
      url: '#'
    },
    {
      title: 'الأسئلة الشائعة',
      description: 'إجابات للأسئلة المتكررة',
      icon: FileText,
      url: '#'
    }
  ];

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-3 mb-4">
            <BookOpen className="h-8 w-8 text-purple-600" />
            الوثائق التقنية
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            مرجع شامل لجميع ميزات نظام MTBRMG ERP مع أدلة مفصلة للمستخدمين والمطورين والمديرين.
          </p>
        </div>

        {/* Version Info */}
        <Card className="mb-8 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Zap className="h-6 w-6 text-purple-600" />
                <div>
                  <h3 className="font-semibold text-gray-900">الإصدار الحالي: v1.0.0</h3>
                  <p className="text-sm text-gray-600">آخر تحديث: {new Date().toLocaleDateString('ar-EG')}</p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 ml-2" />
                  تحميل PDF
                </Button>
                <Button size="sm">
                  <Globe className="h-4 w-4 ml-2" />
                  عرض أونلاين
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Documentation Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {documentationSections.map((section, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-3">
                    <section.icon className="h-6 w-6 text-purple-600" />
                    {section.title}
                  </CardTitle>
                  <Badge variant={section.badgeVariant}>{section.badge}</Badge>
                </div>
                <CardDescription>{section.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 mb-4">
                  {section.items.map((item, itemIndex) => (
                    <li key={itemIndex}>
                      <button className="flex items-center justify-between w-full text-right p-2 rounded hover:bg-gray-50 transition-colors">
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-700">{item}</span>
                      </button>
                    </li>
                  ))}
                </ul>
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => alert(`${section.title} - قريباً`)}
                >
                  عرض الدليل كاملاً
                  <ExternalLink className="h-4 w-4 mr-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Links */}
        <Card>
          <CardHeader>
            <CardTitle>روابط سريعة</CardTitle>
            <CardDescription>
              وصول سريع للموارد الأكثر استخداماً
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {quickLinks.map((link, index) => (
                <button
                  key={index}
                  onClick={() => alert(`${link.title} - قريباً`)}
                  className="p-4 border rounded-lg hover:bg-gray-50 transition-colors text-center"
                >
                  <link.icon className="h-8 w-8 text-purple-600 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">{link.title}</h3>
                  <p className="text-sm text-gray-600">{link.description}</p>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 mb-4">
            هل تحتاج مساعدة في فهم الوثائق؟
          </p>
          <div className="flex justify-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => router.push('/help')}>
              مركز المساعدة
            </Button>
            <Button variant="ghost" size="sm" onClick={() => router.push('/contact')}>
              اتصل بالدعم
            </Button>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  );
}
