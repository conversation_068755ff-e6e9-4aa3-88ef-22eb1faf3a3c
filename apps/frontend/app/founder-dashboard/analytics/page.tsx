'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  FolderOpen,
  CheckSquare,
  Calendar,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';

export default function AnalyticsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [timeRange, setTimeRange] = useState('30d');

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل التقارير والإحصائيات...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى التقارير والإحصائيات</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Mock analytics data
  const analyticsData = {
    revenue: {
      current: 125000,
      previous: 98000,
      change: 27.6
    },
    clients: {
      current: 45,
      previous: 38,
      change: 18.4
    },
    projects: {
      current: 23,
      previous: 19,
      change: 21.1
    },
    tasks: {
      current: 156,
      previous: 142,
      change: 9.9
    }
  };

  const getChangeIcon = (change: number) => {
    return change > 0 ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    );
  };

  const getChangeColor = (change: number) => {
    return change > 0 ? 'text-green-600' : 'text-red-600';
  };

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <BarChart3 className="h-8 w-8 text-purple-600" />
              التقارير والإحصائيات
            </h1>
            <p className="text-gray-600 mt-1">
              تحليل شامل لأداء الوكالة الرقمية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 ml-2" />
              تحديث البيانات
            </Button>
            <Button>
              <Download className="h-4 w-4 ml-2" />
              تصدير التقرير
            </Button>
          </div>
        </div>

        {/* Time Range Filter */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700">الفترة الزمنية:</span>
              <div className="flex gap-2">
                <Button
                  variant={timeRange === '7d' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('7d')}
                >
                  آخر 7 أيام
                </Button>
                <Button
                  variant={timeRange === '30d' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('30d')}
                >
                  آخر 30 يوم
                </Button>
                <Button
                  variant={timeRange === '90d' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('90d')}
                >
                  آخر 3 أشهر
                </Button>
                <Button
                  variant={timeRange === '1y' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange('1y')}
                >
                  آخر سنة
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Revenue */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الإيرادات</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analyticsData.revenue.current.toLocaleString()} ر.س</div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                {getChangeIcon(analyticsData.revenue.change)}
                <span className={getChangeColor(analyticsData.revenue.change)}>
                  {analyticsData.revenue.change > 0 ? '+' : ''}{analyticsData.revenue.change}%
                </span>
                <span>من الشهر الماضي</span>
              </div>
            </CardContent>
          </Card>

          {/* Clients */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العملاء النشطين</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analyticsData.clients.current}</div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                {getChangeIcon(analyticsData.clients.change)}
                <span className={getChangeColor(analyticsData.clients.change)}>
                  {analyticsData.clients.change > 0 ? '+' : ''}{analyticsData.clients.change}%
                </span>
                <span>من الشهر الماضي</span>
              </div>
            </CardContent>
          </Card>

          {/* Projects */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المشاريع النشطة</CardTitle>
              <FolderOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analyticsData.projects.current}</div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                {getChangeIcon(analyticsData.projects.change)}
                <span className={getChangeColor(analyticsData.projects.change)}>
                  {analyticsData.projects.change > 0 ? '+' : ''}{analyticsData.projects.change}%
                </span>
                <span>من الشهر الماضي</span>
              </div>
            </CardContent>
          </Card>

          {/* Tasks */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المهام المكتملة</CardTitle>
              <CheckSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analyticsData.tasks.current}</div>
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                {getChangeIcon(analyticsData.tasks.change)}
                <span className={getChangeColor(analyticsData.tasks.change)}>
                  {analyticsData.tasks.change > 0 ? '+' : ''}{analyticsData.tasks.change}%
                </span>
                <span>من الشهر الماضي</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Revenue Chart */}
          <Card>
            <CardHeader>
              <CardTitle>نمو الإيرادات</CardTitle>
              <CardDescription>الإيرادات الشهرية خلال العام الماضي</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">مخطط الإيرادات</p>
                  <p className="text-sm text-gray-500">سيتم تطبيق المخططات قريباً</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Project Status Chart */}
          <Card>
            <CardHeader>
              <CardTitle>حالة المشاريع</CardTitle>
              <CardDescription>توزيع المشاريع حسب الحالة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">مخطط المشاريع</p>
                  <p className="text-sm text-gray-500">سيتم تطبيق المخططات قريباً</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>النشاط الأخير</CardTitle>
            <CardDescription>آخر الأنشطة في النظام</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">تم إكمال مشروع "تطوير موقع الشركة"</p>
                  <p className="text-xs text-gray-600">منذ ساعتين</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">تم إضافة عميل جديد "شركة التقنية المتقدمة"</p>
                  <p className="text-xs text-gray-600">منذ 4 ساعات</p>
                </div>
              </div>
              <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">تم تعيين مهمة جديدة لأحمد محمد</p>
                  <p className="text-xs text-gray-600">منذ 6 ساعات</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </UnifiedLayout>
  );
}
