'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  UserCheck,
  Search,
  Plus,
  Mail,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  UserPlus
} from 'lucide-react';
import { DEMO_TEAM } from '@/lib/demo-data';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function TeamPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل إدارة الفريق...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى إدارة الفريق</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Filter team members based on search and role
  const filteredMembers = DEMO_TEAM.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || member.role === roleFilter;
    return matchesSearch && matchesRole;
  });

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'sales_manager':
        return <Badge className="bg-blue-100 text-blue-800">مدير مبيعات</Badge>;
      case 'media_buyer':
        return <Badge className="bg-orange-100 text-orange-800">مشتري إعلانات</Badge>;
      case 'developer':
        return <Badge className="bg-green-100 text-green-800">مطور</Badge>;
      case 'designer':
        return <Badge className="bg-pink-100 text-pink-800">مصمم</Badge>;
      case 'wordpress_developer':
        return <Badge className="bg-purple-100 text-purple-800">مطور ووردبريس</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };



  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <UserCheck className="h-8 w-8 text-purple-600" />
              إدارة الفريق
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة شاملة لجميع أعضاء فريق الوكالة الرقمية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button>
              <UserPlus className="h-4 w-4 ml-2" />
              إضافة عضو جديد
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في أعضاء الفريق..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={roleFilter === 'all' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('all')}
                >
                  الكل ({DEMO_TEAM.length})
                </Button>
                <Button
                  variant={roleFilter === 'developer' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('developer')}
                >
                  مطورين ({DEMO_TEAM.filter(m => m.role === 'developer' || m.role === 'wordpress_developer').length})
                </Button>
                <Button
                  variant={roleFilter === 'designer' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('designer')}
                >
                  مصممين ({DEMO_TEAM.filter(m => m.role === 'designer').length})
                </Button>
                <Button
                  variant={roleFilter === 'sales_manager' ? 'default' : 'outline'}
                  onClick={() => setRoleFilter('sales_manager')}
                >
                  مبيعات ({DEMO_TEAM.filter(m => m.role === 'sales_manager' || m.role === 'media_buyer').length})
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMembers.map((member) => (
            <Card key={member.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 font-bold text-lg">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{member.name}</CardTitle>
                      <p className="text-sm text-gray-600">{getRoleBadge(member.role)}</p>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="h-4 w-4 ml-2" />
                        عرض الملف الشخصي
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="h-4 w-4 ml-2" />
                        تعديل
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Mail className="h-4 w-4" />
                    <span>{member.email}</span>
                  </div>

                  {/* Role-specific stats */}
                  {member.role === 'sales_manager' && (
                    <>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">العملاء:</span>
                        <span className="font-medium">{member.clients_count}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">الإيرادات المحققة:</span>
                        <span className="font-medium">{member.revenue_generated?.toLocaleString()} ر.س</span>
                      </div>
                    </>
                  )}

                  {member.role === 'media_buyer' && (
                    <>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">الحملات المدارة:</span>
                        <span className="font-medium">{member.campaigns_managed}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">الإنفاق الإعلاني:</span>
                        <span className="font-medium">{member.ad_spend?.toLocaleString()} ر.س</span>
                      </div>
                    </>
                  )}

                  {(member.role === 'developer' || member.role === 'wordpress_developer') && (
                    <>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">المشاريع المكتملة:</span>
                        <span className="font-medium">{member.projects_completed || member.sites_built}</span>
                      </div>
                      {member.technologies && (
                        <div className="text-sm">
                          <span className="text-gray-600">التقنيات:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {member.technologies.map((tech, index) => (
                              <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                {tech}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      {member.plugins_developed && (
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">الإضافات المطورة:</span>
                          <span className="font-medium">{member.plugins_developed}</span>
                        </div>
                      )}
                    </>
                  )}

                  {member.role === 'designer' && (
                    <>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">التصاميم المكتملة:</span>
                        <span className="font-medium">{member.designs_completed}</span>
                      </div>
                      {member.specialties && (
                        <div className="text-sm">
                          <span className="text-gray-600">التخصصات:</span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {member.specialties.map((specialty, index) => (
                              <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                {specialty}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredMembers.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <UserCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا يوجد أعضاء فريق</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || roleFilter !== 'all' 
                  ? 'لا يوجد أعضاء فريق يطابقون معايير البحث المحددة'
                  : 'لم يتم إضافة أي أعضاء فريق بعد'
                }
              </p>
              {(!searchTerm && roleFilter === 'all') && (
                <Button>
                  <UserPlus className="h-4 w-4 ml-2" />
                  إضافة أول عضو فريق
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}
