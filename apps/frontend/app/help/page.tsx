'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  HelpCircle,
  Search,
  Book,
  MessageCircle,
  Phone,
  Mail,
  ExternalLink,
  ArrowRight,
  ChevronRight,
  FileText,
  Video,
  Users
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';

export default function HelpPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل مركز المساعدة...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  const helpCategories = [
    {
      title: 'البدء السريع',
      description: 'تعلم أساسيات استخدام النظام',
      icon: Book,
      articles: [
        'كيفية إنشاء حساب جديد',
        'جولة في لوحة التحكم',
        'إعداد الملف الشخصي',
        'فهم الأدوار والصلاحيات'
      ]
    },
    {
      title: 'إدارة العملاء',
      description: 'كل ما تحتاج لمعرفته عن إدارة العملاء',
      icon: Users,
      articles: [
        'إضافة عميل جديد',
        'تتبع حالة العملاء',
        'إدارة معلومات الاتصال',
        'تقارير العملاء'
      ]
    },
    {
      title: 'إدارة المشاريع',
      description: 'دليل شامل لإدارة المشاريع',
      icon: FileText,
      articles: [
        'إنشاء مشروع جديد',
        'تعيين المهام للفريق',
        'تتبع التقدم',
        'إدارة الميزانيات'
      ]
    },
    {
      title: 'التقارير والإحصائيات',
      description: 'فهم البيانات والتقارير',
      icon: Video,
      articles: [
        'قراءة التقارير المالية',
        'تحليل أداء الفريق',
        'إحصائيات العملاء',
        'تصدير البيانات'
      ]
    }
  ];

  const contactOptions = [
    {
      title: 'الدردشة المباشرة',
      description: 'تحدث مع فريق الدعم الآن',
      icon: MessageCircle,
      action: 'بدء المحادثة',
      available: true
    },
    {
      title: 'البريد الإلكتروني',
      description: 'أرسل لنا رسالة وسنرد خلال 24 ساعة',
      icon: Mail,
      action: 'إرسال رسالة',
      available: true
    },
    {
      title: 'الهاتف',
      description: 'اتصل بنا مباشرة للدعم العاجل',
      icon: Phone,
      action: 'اتصل الآن',
      available: false
    }
  ];

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-3 mb-4">
            <HelpCircle className="h-8 w-8 text-purple-600" />
            مركز المساعدة
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            مرحباً بك في مركز المساعدة! هنا ستجد كل ما تحتاجه لاستخدام نظام MTBRMG ERP بكفاءة.
          </p>
        </div>

        {/* Search */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="relative max-w-md mx-auto">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="ابحث في المساعدة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-12 text-lg py-3"
              />
            </div>
          </CardContent>
        </Card>

        {/* Help Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {helpCategories.map((category, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <category.icon className="h-6 w-6 text-purple-600" />
                  {category.title}
                </CardTitle>
                <CardDescription>{category.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {category.articles.map((article, articleIndex) => (
                    <li key={articleIndex}>
                      <button className="flex items-center justify-between w-full text-right p-2 rounded hover:bg-gray-50 transition-colors">
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-700">{article}</span>
                      </button>
                    </li>
                  ))}
                </ul>
                <Button variant="outline" className="w-full mt-4">
                  عرض المزيد
                  <ArrowRight className="h-4 w-4 mr-2" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contact Support */}
        <Card>
          <CardHeader>
            <CardTitle>تحتاج مساعدة إضافية؟</CardTitle>
            <CardDescription>
              فريق الدعم الفني متاح لمساعدتك في أي وقت
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {contactOptions.map((option, index) => (
                <div key={index} className="text-center p-4 border rounded-lg">
                  <option.icon className="h-8 w-8 text-purple-600 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">{option.title}</h3>
                  <p className="text-sm text-gray-600 mb-4">{option.description}</p>
                  <Button 
                    variant={option.available ? "default" : "outline"} 
                    disabled={!option.available}
                    onClick={() => {
                      if (option.available) {
                        alert(`${option.title} - هذه الميزة قيد التطوير`);
                      }
                    }}
                  >
                    {option.action}
                    {option.available && <ExternalLink className="h-4 w-4 mr-2" />}
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Links */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 mb-4">روابط مفيدة</p>
          <div className="flex justify-center gap-4 flex-wrap">
            <Button variant="ghost" size="sm" onClick={() => router.push('/docs')}>
              الوثائق التقنية
            </Button>
            <Button variant="ghost" size="sm" onClick={() => router.push('/contact')}>
              اتصل بنا
            </Button>
            <Button variant="ghost" size="sm" onClick={() => router.push('/privacy')}>
              سياسة الخصوصية
            </Button>
          </div>
        </div>
      </div>
    </UnifiedLayout>
  );
}
