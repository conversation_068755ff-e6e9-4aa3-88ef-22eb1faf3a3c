'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Shield,
  Lock,
  Eye,
  Database,
  Users,
  FileText,
  Calendar,
  Mail,
  Phone
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';

export default function PrivacyPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل سياسة الخصوصية...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  const privacySections = [
    {
      title: 'جمع المعلومات',
      icon: Database,
      content: [
        'نجمع المعلومات التي تقدمها لنا مباشرة عند إنشاء حساب أو استخدام خدماتنا',
        'معلومات الاتصال مثل الاسم والبريد الإلكتروني ورقم الهاتف',
        'معلومات الشركة والمشاريع التي تدخلها في النظام',
        'بيانات الاستخدام وسجلات النشاط لتحسين الخدمة'
      ]
    },
    {
      title: 'استخدام المعلومات',
      icon: Eye,
      content: [
        'تقديم وتشغيل خدمات نظام إدارة الوكالة الرقمية',
        'تخصيص تجربة المستخدم وتحسين الخدمات',
        'التواصل معك بشأن حسابك والتحديثات المهمة',
        'ضمان أمان النظام ومنع الاستخدام غير المصرح به'
      ]
    },
    {
      title: 'حماية البيانات',
      icon: Lock,
      content: [
        'نستخدم تشفير SSL/TLS لحماية البيانات أثناء النقل',
        'تشفير البيانات الحساسة في قاعدة البيانات',
        'وصول محدود للموظفين المصرح لهم فقط',
        'نسخ احتياطية منتظمة وآمنة للبيانات'
      ]
    },
    {
      title: 'مشاركة المعلومات',
      icon: Users,
      content: [
        'لا نبيع أو نؤجر معلوماتك الشخصية لأطراف ثالثة',
        'قد نشارك المعلومات مع مقدمي الخدمات الموثوقين',
        'الكشف عن المعلومات عند الضرورة القانونية فقط',
        'حماية حقوق وأمان المستخدمين والشركة'
      ]
    },
    {
      title: 'حقوق المستخدم',
      icon: Shield,
      content: [
        'الحق في الوصول إلى بياناتك الشخصية',
        'طلب تصحيح أو تحديث المعلومات غير الصحيحة',
        'حذف حسابك وبياناتك (مع مراعاة المتطلبات القانونية)',
        'الاعتراض على معالجة بياناتك في ظروف معينة'
      ]
    },
    {
      title: 'ملفات تعريف الارتباط',
      icon: FileText,
      content: [
        'نستخدم ملفات تعريف الارتباط لتحسين تجربة المستخدم',
        'تذكر تفضيلاتك وإعدادات تسجيل الدخول',
        'تحليل استخدام الموقع لتحسين الخدمات',
        'يمكنك التحكم في ملفات تعريف الارتباط من إعدادات المتصفح'
      ]
    }
  ];

  const lastUpdated = new Date().toLocaleDateString('ar-EG');

  return (
    <UnifiedLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-3 mb-4">
            <Shield className="h-8 w-8 text-purple-600" />
            سياسة الخصوصية
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            نحن ملتزمون بحماية خصوصيتك وأمان بياناتك. تشرح هذه السياسة كيفية جمع واستخدام وحماية معلوماتك.
          </p>
        </div>

        {/* Last Updated */}
        <Card className="mb-8 bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-blue-800">
              <Calendar className="h-4 w-4" />
              <span className="text-sm">آخر تحديث: {lastUpdated}</span>
            </div>
          </CardContent>
        </Card>

        {/* Privacy Sections */}
        <div className="space-y-6 mb-8">
          {privacySections.map((section, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <section.icon className="h-6 w-6 text-purple-600" />
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {section.content.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-700">{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contact Information */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>تواصل معنا</CardTitle>
            <CardDescription>
              إذا كان لديك أي أسئلة حول سياسة الخصوصية، يرجى التواصل معنا
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-semibold">البريد الإلكتروني</p>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-purple-600" />
                <div>
                  <p className="font-semibold">الهاتف</p>
                  <p className="text-gray-600">+20 ************</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Important Notice */}
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-6">
            <h3 className="font-semibold text-yellow-800 mb-2">ملاحظة مهمة</h3>
            <p className="text-yellow-700 text-sm">
              قد نقوم بتحديث سياسة الخصوصية من وقت لآخر. سنقوم بإشعارك بأي تغييرات مهمة عبر البريد الإلكتروني أو من خلال إشعار في النظام. 
              يُعتبر استمرارك في استخدام الخدمة بعد هذه التغييرات موافقة على السياسة المحدثة.
            </p>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="mt-8 text-center space-y-4">
          <div className="flex justify-center gap-4">
            <Button onClick={() => router.push('/contact')}>
              <Mail className="h-4 w-4 ml-2" />
              اتصل بنا
            </Button>
            <Button variant="outline" onClick={() => router.push('/help')}>
              مركز المساعدة
            </Button>
          </div>
          <p className="text-sm text-gray-500">
            بالمتابعة في استخدام نظام MTBRMG ERP، فإنك توافق على سياسة الخصوصية هذه.
          </p>
        </div>
      </div>
    </UnifiedLayout>
  );
}
