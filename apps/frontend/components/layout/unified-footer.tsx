'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Heart, ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';

interface UnifiedFooterProps {
  className?: string;
}

export function UnifiedFooter({ className }: UnifiedFooterProps) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={cn("bg-white border-t border-gray-200 py-6 px-4 lg:px-6", className)}>
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Company Info */}
          <div className="md:col-span-2">
            <div className="flex items-center gap-3 mb-4">
              <div className="relative w-8 h-8">
                <Image
                  src="/the_logo.png"
                  alt="MTBRMG Logo"
                  fill
                  className="object-contain"
                />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">MTBRMG ERP</h3>
                <p className="text-sm text-gray-500">نظام إدارة الوكالة الرقمية</p>
              </div>
            </div>
            <p className="text-sm text-gray-600 mb-4">
              نظام إدارة شامل للوكالة الرقمية المصرية مع دعم كامل للغة العربية. 
              يساعدك على إدارة العملاء والمشاريع والمهام والفريق بكفاءة عالية.
            </p>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <span>صُنع بـ</span>
              <Heart className="h-4 w-4 text-red-500 fill-current" />
              <span>في مصر</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 mb-3">روابط سريعة</h4>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/founder-dashboard"
                  className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                >
                  لوحة التحكم
                </Link>
              </li>
              <li>
                <Link
                  href="/founder-dashboard/clients"
                  className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                >
                  إدارة العملاء
                </Link>
              </li>
              <li>
                <Link
                  href="/founder-dashboard/projects"
                  className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                >
                  إدارة المشاريع
                </Link>
              </li>
              <li>
                <Link
                  href="/founder-dashboard/analytics"
                  className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                >
                  التقارير والإحصائيات
                </Link>
              </li>
            </ul>
          </div>

          {/* Support & Resources */}
          <div>
            <h4 className="text-sm font-semibold text-gray-900 mb-3">الدعم والموارد</h4>
            <ul className="space-y-2">
              <li>
                <button
                  onClick={() => alert('مركز المساعدة - قريباً')}
                  className="text-sm text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-1"
                >
                  مركز المساعدة
                  <ExternalLink className="h-3 w-3" />
                </button>
              </li>
              <li>
                <button
                  onClick={() => alert('الوثائق - قريباً')}
                  className="text-sm text-gray-600 hover:text-gray-900 transition-colors flex items-center gap-1"
                >
                  الوثائق
                  <ExternalLink className="h-3 w-3" />
                </button>
              </li>
              <li>
                <button
                  onClick={() => alert('اتصل بنا - قريباً')}
                  className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                >
                  اتصل بنا
                </button>
              </li>
              <li>
                <button
                  onClick={() => alert('سياسة الخصوصية - قريباً')}
                  className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                >
                  سياسة الخصوصية
                </button>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-gray-500">
              © {currentYear} MTBRMG ERP. جميع الحقوق محفوظة.
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>الإصدار 1.0.0</span>
              <span>•</span>
              <span>آخر تحديث: {new Date().toLocaleDateString('ar-EG')}</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
