'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { 
  Home, 
  Users, 
  FolderOpen, 
  CheckSquare, 
  UserCheck,
  BarChart3,
  Settings,
  Shield,
  Crown,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useAuthStore } from '@/lib/stores/auth-store';
import { UserRole } from '@mtbrmg/shared';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles: UserRole[];
  badge?: string;
  children?: NavigationItem[];
}

const navigationItems: NavigationItem[] = [
  {
    name: 'لوحة تحكم المؤسس',
    href: '/founder-dashboard',
    icon: Crown,
    roles: [UserRole.ADMIN],
    badge: 'مؤسس',
  },
  {
    name: 'إدارة العملاء',
    href: '/founder-dashboard/clients',
    icon: Users,
    roles: [UserRole.ADMIN],
  },
  {
    name: 'إدارة المشاريع',
    href: '/founder-dashboard/projects',
    icon: FolderOpen,
    roles: [UserRole.ADMIN],
  },
  {
    name: 'إدارة المهام',
    href: '/founder-dashboard/tasks',
    icon: CheckSquare,
    roles: [UserRole.ADMIN],
  },
  {
    name: 'إدارة الفريق',
    href: '/founder-dashboard/team',
    icon: UserCheck,
    roles: [UserRole.ADMIN],
  },
  {
    name: 'التقارير والإحصائيات',
    href: '/founder-dashboard/analytics',
    icon: BarChart3,
    roles: [UserRole.ADMIN],
  },
  {
    name: 'الإعدادات',
    href: '/founder-dashboard/settings',
    icon: Settings,
    roles: [UserRole.ADMIN],
  },
];

interface UnifiedSidebarProps {
  className?: string;
}

export function UnifiedSidebar({ className }: UnifiedSidebarProps) {
  const pathname = usePathname();
  const { user } = useAuthStore();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  const hasAccess = (roles: UserRole[]) => {
    if (!user) return false;
    return roles.includes(user.role as UserRole);
  };

  const isActive = (href: string) => {
    if (href === '/founder-dashboard') {
      return pathname === '/founder-dashboard';
    }
    return pathname.startsWith(href);
  };

  const filteredNavigation = navigationItems.filter(item => hasAccess(item.roles));

  return (
    <div className={cn("h-full flex flex-col bg-white border-l border-gray-200", className)}>
      {/* Logo Section */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="relative w-10 h-10">
            <Image
              src="/the_logo.png"
              alt="MTBRMG Logo"
              fill
              className="object-contain"
            />
          </div>
          <div>
            <h1 className="text-lg font-bold text-gray-900">MTBRMG ERP</h1>
            <p className="text-sm text-gray-500">نظام إدارة الوكالة</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-6 space-y-2 overflow-y-auto">
        {filteredNavigation.map((item) => {
          const isItemActive = isActive(item.href);
          const isExpanded = expandedItems.includes(item.name);
          const hasChildren = item.children && item.children.length > 0;

          if (hasChildren) {
            return (
              <Collapsible key={item.name} open={isExpanded} onOpenChange={() => toggleExpanded(item.name)}>
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-between px-4 py-3 text-sm font-medium rounded-lg transition-colors",
                      isItemActive
                        ? "bg-blue-50 text-blue-700"
                        : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <item.icon className="h-5 w-5" />
                      <span>{item.name}</span>
                      {item.badge && (
                        <Badge variant="secondary" className="text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-1 mt-1">
                  {item.children?.filter(child => hasAccess(child.roles)).map((child) => (
                    <Link
                      key={child.name}
                      href={child.href}
                      className={cn(
                        "flex items-center gap-3 px-4 py-2 mr-6 text-sm font-medium rounded-lg transition-colors",
                        isActive(child.href)
                          ? "bg-blue-50 text-blue-700"
                          : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                      )}
                    >
                      <child.icon className="h-4 w-4" />
                      <span>{child.name}</span>
                    </Link>
                  ))}
                </CollapsibleContent>
              </Collapsible>
            );
          }

          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-lg transition-colors",
                isItemActive
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
              )}
            >
              <item.icon className="h-5 w-5" />
              <span>{item.name}</span>
              {item.badge && (
                <Badge variant="secondary" className="text-xs">
                  {item.badge}
                </Badge>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Quick Stats Section */}
      <div className="p-6 border-t border-gray-200">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
          <h3 className="text-sm font-semibold text-gray-900 mb-4">الإحصائيات السريعة</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">المشاريع النشطة</span>
              <span className="text-sm font-bold text-blue-600">12</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">المهام المعلقة</span>
              <span className="text-sm font-bold text-orange-600">8</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">العملاء الجدد</span>
              <span className="text-sm font-bold text-green-600">3</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
