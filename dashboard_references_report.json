{"dashboard/": [{"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "46", "content": "href: '/founder-dashboard/clients',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "52", "content": "href: '/founder-dashboard/projects',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "58", "content": "href: '/founder-dashboard/tasks',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "64", "content": "href: '/founder-dashboard/team',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "70", "content": "href: '/founder-dashboard/analytics',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "76", "content": "href: '/founder-dashboard/settings',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-footer.tsx", "line": "60", "content": "href=\"/founder-dashboard/clients\""}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-footer.tsx", "line": "68", "content": "href=\"/founder-dashboard/projects\""}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-footer.tsx", "line": "76", "content": "href=\"/founder-dashboard/analytics\""}], "href.*dashboard": [{"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/auth-test/page.tsx", "line": "48", "content": "<a href=\"/founder-dashboard\" className=\"block text-blue-500 underline\">Go to Founder Dashboard</a>"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "39", "content": "href: '/founder-dashboard',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "46", "content": "href: '/founder-dashboard/clients',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "52", "content": "href: '/founder-dashboard/projects',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "58", "content": "href: '/founder-dashboard/tasks',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "64", "content": "href: '/founder-dashboard/team',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "70", "content": "href: '/founder-dashboard/analytics',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "76", "content": "href: '/founder-dashboard/settings',"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-sidebar.tsx", "line": "105", "content": "if (href === '/founder-dashboard') {"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-footer.tsx", "line": "52", "content": "href=\"/founder-dashboard\""}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-footer.tsx", "line": "60", "content": "href=\"/founder-dashboard/clients\""}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-footer.tsx", "line": "68", "content": "href=\"/founder-dashboard/projects\""}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/layout/unified-footer.tsx", "line": "76", "content": "href=\"/founder-dashboard/analytics\""}], "to.*dashboard": [{"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/auth-test/page.tsx", "line": "48", "content": "<a href=\"/founder-dashboard\" className=\"block text-blue-500 underline\">Go to Founder Dashboard</a>"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/login/page.tsx", "line": "52", "content": "// Always redirect to founder dashboard (single dashboard system)"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/components/auth/role-guard.tsx", "line": "18", "content": "redirectTo = '/founder-dashboard',"}], "push.*dashboard": [{"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/page.tsx", "line": "19", "content": "router.push('/founder-dashboard');"}, {"file": "/Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/app/login/page.tsx", "line": "53", "content": "router.push('/founder-dashboard');"}]}