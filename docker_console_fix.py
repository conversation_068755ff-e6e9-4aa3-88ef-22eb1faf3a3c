#!/usr/bin/env python3
"""
Docker-Compatible Console Errors Fixer for MTBRMG ERP System
This script fixes console errors in a Docker environment.
"""

import os
import subprocess
import time
from pathlib import Path
import json

class DockerConsoleFixer:
    def __init__(self, project_root: str = "/Users/<USER>/Sites/mtbrmg-erp-system"):
        self.project_root = Path(project_root)
        self.frontend_path = self.project_root / "apps" / "frontend"
        
    def check_docker_status(self):
        """Check if Docker containers are running."""
        print("🐳 Checking Docker status...")
        
        try:
            result = subprocess.run(["docker", "ps"], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Docker is running")
                print("📋 Running containers:")
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:  # Skip header
                    if 'mtbrmg' in line.lower() or 'frontend' in line.lower():
                        print(f"  🔹 {line}")
                return True
            else:
                print("❌ Docker is not running or accessible")
                return False
        except FileNotFoundError:
            print("❌ Docker command not found")
            return False
        except Exception as e:
            print(f"❌ Error checking Docker: {e}")
            return False
    
    def restart_docker_containers(self):
        """Restart Docker containers to clear cache."""
        print("🔄 Restarting Docker containers...")
        
        try:
            # Stop containers
            subprocess.run(["docker-compose", "down"], 
                         cwd=self.project_root, capture_output=True)
            print("  ✅ Containers stopped")
            
            time.sleep(3)
            
            # Start containers
            subprocess.run(["docker-compose", "up", "-d"], 
                         cwd=self.project_root, capture_output=True)
            print("  ✅ Containers started")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Error restarting containers: {e}")
            return False
    
    def clear_docker_cache(self):
        """Clear Docker build cache."""
        print("🧹 Clearing Docker cache...")
        
        try:
            # Clear build cache
            subprocess.run(["docker", "builder", "prune", "-f"], 
                         capture_output=True)
            print("  ✅ Docker build cache cleared")
            
            # Clear unused images
            subprocess.run(["docker", "image", "prune", "-f"], 
                         capture_output=True)
            print("  ✅ Unused Docker images removed")
            
        except Exception as e:
            print(f"  ❌ Error clearing Docker cache: {e}")
    
    def check_route_files_exist(self):
        """Verify all route files exist."""
        print("📁 Verifying route files...")
        
        required_routes = {
            "/help": self.frontend_path / "app" / "help" / "page.tsx",
            "/docs": self.frontend_path / "app" / "docs" / "page.tsx", 
            "/contact": self.frontend_path / "app" / "contact" / "page.tsx",
            "/privacy": self.frontend_path / "app" / "privacy" / "page.tsx"
        }
        
        all_exist = True
        for route, file_path in required_routes.items():
            exists = file_path.exists()
            status = "✅" if exists else "❌"
            print(f"  {status} {route}: {file_path.name}")
            if not exists:
                all_exist = False
        
        return all_exist
    
    def create_docker_ignore_update(self):
        """Update .dockerignore to prevent cache issues."""
        print("📝 Updating .dockerignore...")
        
        dockerignore_path = self.frontend_path / ".dockerignore"
        
        ignore_content = """# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/
build/
dist/

# Cache directories
.cache/
.turbo/
.swc/

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Temporary folders
tmp/
temp/
"""
        
        try:
            with open(dockerignore_path, 'w', encoding='utf-8') as f:
                f.write(ignore_content)
            print("  ✅ .dockerignore updated")
        except Exception as e:
            print(f"  ❌ Error updating .dockerignore: {e}")
    
    def wait_for_container_ready(self, timeout=60):
        """Wait for the frontend container to be ready."""
        print("⏳ Waiting for container to be ready...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                result = subprocess.run(
                    ["curl", "-f", "http://localhost:3001"], 
                    capture_output=True, 
                    timeout=5
                )
                if result.returncode == 0:
                    print("  ✅ Container is ready!")
                    return True
            except:
                pass
            
            print("  ⏳ Still waiting...")
            time.sleep(5)
        
        print("  ⚠️  Timeout waiting for container")
        return False
    
    def provide_docker_instructions(self):
        """Provide Docker-specific instructions."""
        print("\n🐳 DOCKER-SPECIFIC INSTRUCTIONS:")
        print("="*50)
        
        print("\n🔧 If errors persist in Docker:")
        print("1. Clear browser cache completely")
        print("2. Use incognito/private browsing mode")
        print("3. Check Docker logs:")
        print("   docker-compose logs frontend")
        print("4. Rebuild containers if needed:")
        print("   docker-compose down")
        print("   docker-compose build --no-cache")
        print("   docker-compose up -d")
        
        print("\n🌐 Browser Cache Clearing:")
        print("- Open DevTools (F12)")
        print("- Right-click refresh → 'Empty Cache and Hard Reload'")
        print("- Or use Ctrl+Shift+R (hard refresh)")
        
        print("\n🔍 Debugging Steps:")
        print("1. Open http://localhost:3001 in incognito mode")
        print("2. Open DevTools → Console tab")
        print("3. Look for any remaining 404 errors")
        print("4. Check Network tab for failed requests")
        print("5. Navigate to /help, /docs, /contact, /privacy")
    
    def run_docker_fix(self):
        """Run the complete Docker-compatible fix."""
        print("🚀 STARTING DOCKER CONSOLE ERRORS FIX")
        print("="*50)
        
        # Step 1: Check Docker status
        if not self.check_docker_status():
            print("❌ Docker is not available. Please start Docker first.")
            return False
        
        # Step 2: Verify route files exist
        if not self.check_route_files_exist():
            print("❌ Some route files are missing. Please create them first.")
            return False
        
        # Step 3: Update .dockerignore
        self.create_docker_ignore_update()
        
        # Step 4: Clear Docker cache
        self.clear_docker_cache()
        
        # Step 5: Restart containers
        if self.restart_docker_containers():
            # Step 6: Wait for container to be ready
            if self.wait_for_container_ready():
                print("\n✅ Docker fix completed successfully!")
            else:
                print("\n⚠️  Container may still be starting...")
        else:
            print("\n❌ Failed to restart containers")
            return False
        
        # Step 7: Provide instructions
        self.provide_docker_instructions()
        
        print("\n" + "="*50)
        print("🎉 DOCKER FIX COMPLETED!")
        print("="*50)
        print("\n📋 NEXT STEPS:")
        print("1. Clear browser cache")
        print("2. Open http://localhost:3001 in incognito mode")
        print("3. Check console for errors")
        print("4. Test all routes: /help, /docs, /contact, /privacy")
        
        return True

def main():
    """Main function to run the Docker console errors fixer."""
    try:
        fixer = DockerConsoleFixer()
        success = fixer.run_docker_fix()
        
        if success:
            print("\n🎯 Fix completed! Check the application now.")
        else:
            print("\n❌ Fix failed. Please check the errors above.")
            
    except KeyboardInterrupt:
        print("\n⚠️  Script interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        raise

if __name__ == "__main__":
    main()
