#!/usr/bin/env python3
"""
Final Cache Cleaner for MTBRMG ERP System
This script performs a comprehensive cache cleanup to eliminate console errors.
"""

import os
import subprocess
import time
from pathlib import Path
import shutil

class FinalCacheCleaner:
    def __init__(self, project_root: str = "/Users/<USER>/Sites/mtbrmg-erp-system"):
        self.project_root = Path(project_root)
        self.frontend_path = self.project_root / "apps" / "frontend"
        
    def clear_all_nextjs_caches(self):
        """Clear all possible Next.js caches."""
        print("🧹 Clearing ALL Next.js caches...")
        
        cache_paths = [
            self.frontend_path / ".next",
            self.frontend_path / "node_modules" / ".cache",
            self.frontend_path / ".turbo",
            self.frontend_path / ".swc",
            self.frontend_path / "dist",
            self.frontend_path / "build"
        ]
        
        for cache_path in cache_paths:
            if cache_path.exists():
                try:
                    shutil.rmtree(cache_path)
                    print(f"  ✅ Cleared: {cache_path}")
                except Exception as e:
                    print(f"  ❌ Error clearing {cache_path}: {e}")
    
    def clear_npm_cache(self):
        """Clear npm cache."""
        print("📦 Clearing npm cache...")
        try:
            subprocess.run(["npm", "cache", "clean", "--force"], 
                         cwd=self.frontend_path, check=True, capture_output=True)
            print("  ✅ npm cache cleared")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Error clearing npm cache: {e}")
    
    def clear_browser_caches_instructions(self):
        """Provide instructions for clearing browser caches."""
        print("\n🌐 BROWSER CACHE CLEARING INSTRUCTIONS:")
        print("="*50)
        
        print("\n🔧 Chrome/Edge:")
        print("  1. Open DevTools (F12)")
        print("  2. Right-click refresh button")
        print("  3. Select 'Empty Cache and Hard Reload'")
        print("  4. Or: Settings > Privacy > Clear browsing data")
        
        print("\n🦊 Firefox:")
        print("  1. Ctrl+Shift+Delete")
        print("  2. Select 'Everything' timeframe")
        print("  3. Check 'Cache' and 'Site Data'")
        print("  4. Click 'Clear Now'")
        
        print("\n🍎 Safari:")
        print("  1. Safari > Preferences > Advanced")
        print("  2. Enable 'Show Develop menu'")
        print("  3. Develop > Empty Caches")
        
        print("\n🔒 Alternative - Incognito/Private Mode:")
        print("  1. Open incognito/private window")
        print("  2. Navigate to http://localhost:3001")
        print("  3. Check if errors persist")
    
    def create_cache_buster_middleware(self):
        """Create middleware to prevent caching during development."""
        print("🛡️  Creating cache-busting middleware...")
        
        middleware_content = '''import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Add cache-busting headers for development
  if (process.env.NODE_ENV === 'development') {
    const response = NextResponse.next()
    
    // Prevent caching
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    
    return response
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}'''
        
        middleware_file = self.frontend_path / "middleware.ts"
        try:
            with open(middleware_file, 'w', encoding='utf-8') as f:
                f.write(middleware_content)
            print(f"  ✅ Created cache-busting middleware: {middleware_file}")
        except Exception as e:
            print(f"  ❌ Error creating middleware: {e}")
    
    def update_next_config(self):
        """Update Next.js config to disable caching in development."""
        print("⚙️  Updating Next.js configuration...")
        
        config_file = self.frontend_path / "next.config.mjs"
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Add cache disabling configuration
                if 'generateEtags: false' not in content:
                    # Insert before the closing brace
                    updated_content = content.replace(
                        'export default nextConfig',
                        '''nextConfig.generateEtags = false
nextConfig.poweredByHeader = false

if (process.env.NODE_ENV === 'development') {
  nextConfig.headers = async () => {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
    ]
  }
}

export default nextConfig'''
                    )
                    
                    with open(config_file, 'w', encoding='utf-8') as f:
                        f.write(updated_content)
                    
                    print("  ✅ Updated Next.js config with cache-busting headers")
                else:
                    print("  ✅ Next.js config already has cache-busting configuration")
                    
            except Exception as e:
                print(f"  ❌ Error updating Next.js config: {e}")
    
    def restart_development_server(self):
        """Restart the development server with fresh cache."""
        print("🔄 Restarting development server...")
        
        try:
            # Kill all Next.js processes
            subprocess.run(["pkill", "-f", "next"], capture_output=True)
            time.sleep(3)
            
            # Kill processes on port 3001
            subprocess.run(["lsof", "-ti:3001"], capture_output=True, text=True)
            subprocess.run(["kill", "-9", "$(lsof -ti:3001)"], shell=True, capture_output=True)
            time.sleep(2)
            
            print("  ✅ Killed existing processes")
            
            # Start fresh server
            os.chdir(self.frontend_path)
            print("  🚀 Starting fresh development server...")
            subprocess.Popen(
                ["npm", "run", "dev"], 
                stdout=subprocess.DEVNULL, 
                stderr=subprocess.DEVNULL
            )
            
            print("  ✅ Development server started")
            
        except Exception as e:
            print(f"  ❌ Error restarting server: {e}")
    
    def run_final_cleanup(self):
        """Run the complete final cleanup process."""
        print("🚀 STARTING FINAL CACHE CLEANUP")
        print("="*50)
        
        # Step 1: Clear all caches
        self.clear_all_nextjs_caches()
        self.clear_npm_cache()
        
        # Step 2: Create cache-busting middleware
        self.create_cache_buster_middleware()
        
        # Step 3: Update Next.js config
        self.update_next_config()
        
        # Step 4: Restart server
        self.restart_development_server()
        
        # Step 5: Provide browser instructions
        self.clear_browser_caches_instructions()
        
        print("\n" + "="*50)
        print("🎉 FINAL CLEANUP COMPLETED!")
        print("="*50)
        
        print("\n📋 NEXT STEPS:")
        print("1. Wait 15 seconds for server to fully start")
        print("2. Clear browser cache (see instructions above)")
        print("3. Open http://localhost:3001 in incognito mode")
        print("4. Check browser console - should be error-free!")
        print("5. Navigate through all pages to verify")
        
        print("\n🔍 IF ERRORS PERSIST:")
        print("- The issue might be in browser extensions")
        print("- Try different browser")
        print("- Check network tab for failed requests")
        print("- Disable service workers in DevTools")

def main():
    cleaner = FinalCacheCleaner()
    cleaner.run_final_cleanup()

if __name__ == "__main__":
    main()
