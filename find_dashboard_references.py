#!/usr/bin/env python3
"""
Advanced Dashboard References Finder
This script finds ALL references to /dashboard routes in the codebase.
"""

import os
import re
import subprocess
from pathlib import Path
import json

class DashboardReferencesFinder:
    def __init__(self, project_root: str = "/Users/<USER>/Sites/mtbrmg-erp-system"):
        self.project_root = Path(project_root)
        self.frontend_path = self.project_root / "apps" / "frontend"
        
    def find_all_dashboard_references(self):
        """Find ALL references to /dashboard in the codebase."""
        print("🔍 Searching for ALL /dashboard references...")
        
        # Search patterns
        patterns = [
            r'/dashboard',
            r'dashboard/',
            r'"dashboard"',
            r"'dashboard'",
            r'`dashboard`',
            r'href.*dashboard',
            r'to.*dashboard',
            r'push.*dashboard',
            r'prefetch.*dashboard'
        ]
        
        all_references = {}
        
        for pattern in patterns:
            print(f"\n📋 Searching for pattern: {pattern}")
            references = self._search_pattern(pattern)
            if references:
                all_references[pattern] = references
                
        return all_references
    
    def _search_pattern(self, pattern: str):
        """Search for a specific pattern in all files."""
        try:
            # Use ripgrep if available, otherwise use grep
            cmd = [
                "grep", "-r", "-n", "-i", pattern,
                str(self.frontend_path),
                "--include=*.tsx",
                "--include=*.ts", 
                "--include=*.js",
                "--include=*.jsx",
                "--include=*.json",
                "--exclude-dir=node_modules",
                "--exclude-dir=.next",
                "--exclude-dir=.turbo"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                references = []
                
                for line in lines:
                    if ':' in line:
                        file_path, line_num, content = line.split(':', 2)
                        references.append({
                            'file': file_path,
                            'line': line_num,
                            'content': content.strip()
                        })
                        print(f"  📄 {file_path}:{line_num} -> {content.strip()}")
                
                return references
            else:
                print(f"  ✅ No matches found for: {pattern}")
                return []
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ Search timeout for pattern: {pattern}")
            return []
        except Exception as e:
            print(f"  ❌ Error searching for pattern {pattern}: {e}")
            return []
    
    def check_next_config(self):
        """Check Next.js configuration files."""
        print("\n🔧 Checking Next.js configuration...")
        
        config_files = [
            self.frontend_path / "next.config.mjs",
            self.frontend_path / "next.config.js",
            self.frontend_path / "next.config.ts"
        ]
        
        for config_file in config_files:
            if config_file.exists():
                print(f"📄 Found config: {config_file}")
                with open(config_file, 'r') as f:
                    content = f.read()
                    if 'dashboard' in content.lower():
                        print(f"  ⚠️  Contains 'dashboard' reference!")
                        print(f"  Content preview: {content[:200]}...")
                    else:
                        print(f"  ✅ No dashboard references")
    
    def check_package_json(self):
        """Check package.json for any dashboard-related scripts or dependencies."""
        print("\n📦 Checking package.json...")
        
        package_file = self.frontend_path / "package.json"
        if package_file.exists():
            with open(package_file, 'r') as f:
                content = json.load(f)
                
            # Check scripts
            if 'scripts' in content:
                for script_name, script_content in content['scripts'].items():
                    if 'dashboard' in script_content.lower():
                        print(f"  ⚠️  Script '{script_name}': {script_content}")
            
            # Check dependencies
            all_deps = {}
            all_deps.update(content.get('dependencies', {}))
            all_deps.update(content.get('devDependencies', {}))
            
            for dep_name, dep_version in all_deps.items():
                if 'dashboard' in dep_name.lower():
                    print(f"  ⚠️  Dependency '{dep_name}': {dep_version}")
    
    def check_env_files(self):
        """Check environment files."""
        print("\n🌍 Checking environment files...")
        
        env_files = [
            self.frontend_path / ".env",
            self.frontend_path / ".env.local",
            self.frontend_path / ".env.development",
            self.frontend_path / ".env.production"
        ]
        
        for env_file in env_files:
            if env_file.exists():
                print(f"📄 Found env file: {env_file}")
                with open(env_file, 'r') as f:
                    content = f.read()
                    if 'dashboard' in content.lower():
                        print(f"  ⚠️  Contains 'dashboard' reference!")
                        for line_num, line in enumerate(content.split('\n'), 1):
                            if 'dashboard' in line.lower():
                                print(f"    Line {line_num}: {line}")
    
    def check_middleware(self):
        """Check middleware files."""
        print("\n🛡️  Checking middleware...")
        
        middleware_files = [
            self.frontend_path / "middleware.ts",
            self.frontend_path / "middleware.js",
            self.frontend_path / "src" / "middleware.ts",
            self.frontend_path / "src" / "middleware.js"
        ]
        
        for middleware_file in middleware_files:
            if middleware_file.exists():
                print(f"📄 Found middleware: {middleware_file}")
                with open(middleware_file, 'r') as f:
                    content = f.read()
                    if 'dashboard' in content.lower():
                        print(f"  ⚠️  Contains 'dashboard' reference!")
                        print(f"  Content: {content}")
    
    def generate_comprehensive_report(self):
        """Generate a comprehensive report of all findings."""
        print("\n" + "="*60)
        print("🔍 COMPREHENSIVE DASHBOARD REFERENCES ANALYSIS")
        print("="*60)
        
        # Find all references
        references = self.find_all_dashboard_references()
        
        # Check configurations
        self.check_next_config()
        self.check_package_json()
        self.check_env_files()
        self.check_middleware()
        
        # Summary
        print("\n📊 SUMMARY:")
        total_refs = sum(len(refs) for refs in references.values())
        print(f"  Total dashboard references found: {total_refs}")
        
        if total_refs == 0:
            print("  ✅ No explicit dashboard references found!")
            print("  🤔 The prefetch errors might be caused by:")
            print("     - Browser cache")
            print("     - Next.js internal routing cache")
            print("     - Service worker cache")
            print("     - CDN cache")
        else:
            print("  ⚠️  Found references that need investigation:")
            for pattern, refs in references.items():
                if refs:
                    print(f"    {pattern}: {len(refs)} matches")
        
        print("\n🔧 RECOMMENDED NEXT STEPS:")
        print("  1. Clear browser cache completely")
        print("  2. Clear Next.js cache (already done)")
        print("  3. Check browser DevTools -> Application -> Storage")
        print("  4. Disable browser cache in DevTools")
        print("  5. Try incognito/private browsing mode")
        
        return references

def main():
    finder = DashboardReferencesFinder()
    references = finder.generate_comprehensive_report()
    
    # Save detailed report
    with open("dashboard_references_report.json", "w") as f:
        json.dump(references, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: dashboard_references_report.json")

if __name__ == "__main__":
    main()
