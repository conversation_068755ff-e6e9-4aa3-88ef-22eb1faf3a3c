#!/usr/bin/env python3
"""
Advanced Console Errors Fixer for MTBRMG ERP System
This script analyzes and fixes Next.js console errors systematically.
"""

import os
import re
import json
import subprocess
import time
from pathlib import Path
from typing import List, Dict, Set, Optional
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('console_errors_fix.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ConsoleErrorsFixer:
    def __init__(self, project_root: str = "/Users/<USER>/Sites/mtbrmg-erp-system"):
        self.project_root = Path(project_root)
        self.frontend_path = self.project_root / "apps" / "frontend"
        self.problematic_routes = ["/dashboard", "/help", "/docs", "/contact", "/privacy"]
        self.found_issues = []
        
    def analyze_codebase(self) -> Dict[str, List[str]]:
        """Analyze the codebase for potential sources of console errors."""
        logger.info("🔍 Analyzing codebase for console error sources...")
        
        issues = {
            "hardcoded_links": [],
            "prefetch_references": [],
            "navigation_configs": [],
            "debug_code": [],
            "cache_files": []
        }
        
        # Search for hardcoded links
        self._find_hardcoded_links(issues)
        
        # Search for prefetch configurations
        self._find_prefetch_configs(issues)
        
        # Search for debug code
        self._find_debug_code(issues)
        
        # Find cache files
        self._find_cache_files(issues)
        
        return issues
    
    def _find_hardcoded_links(self, issues: Dict[str, List[str]]):
        """Find hardcoded links that might cause prefetch errors."""
        logger.info("🔗 Searching for hardcoded links...")
        
        search_patterns = [
            r'href=["\'](?:/dashboard|/help|/docs|/contact|/privacy)',
            r'router\.push\(["\'](?:/dashboard|/help|/docs|/contact|/privacy)',
            r'Link.*to=["\'](?:/dashboard|/help|/docs|/contact|/privacy)',
            r'navigate\(["\'](?:/dashboard|/help|/docs|/contact|/privacy)'
        ]
        
        for pattern in search_patterns:
            self._search_in_files(pattern, issues["hardcoded_links"])
    
    def _find_prefetch_configs(self, issues: Dict[str, List[str]]):
        """Find prefetch configurations."""
        logger.info("⚡ Searching for prefetch configurations...")
        
        prefetch_patterns = [
            r'prefetch.*(?:/dashboard|/help|/docs|/contact|/privacy)',
            r'router\.prefetch',
            r'next/link.*prefetch',
            r'_rsc=\w+'
        ]
        
        for pattern in prefetch_patterns:
            self._search_in_files(pattern, issues["prefetch_references"])
    
    def _find_debug_code(self, issues: Dict[str, List[str]]):
        """Find debug code that should be removed."""
        logger.info("🐛 Searching for debug code...")
        
        debug_patterns = [
            r'Debug Test:',
            r'console\.log.*debug',
            r'bg-red-100.*Debug',
            r'className="bg-red-100.*border.*red'
        ]
        
        for pattern in debug_patterns:
            self._search_in_files(pattern, issues["debug_code"])
    
    def _find_cache_files(self, issues: Dict[str, List[str]]):
        """Find Next.js cache files that might need clearing."""
        logger.info("🗂️ Searching for cache files...")
        
        cache_paths = [
            self.frontend_path / ".next",
            self.frontend_path / "node_modules" / ".cache",
            self.frontend_path / ".turbo"
        ]
        
        for cache_path in cache_paths:
            if cache_path.exists():
                issues["cache_files"].append(str(cache_path))
    
    def _search_in_files(self, pattern: str, results_list: List[str]):
        """Search for a pattern in all relevant files."""
        file_extensions = [".tsx", ".ts", ".js", ".jsx", ".json"]
        
        for ext in file_extensions:
            try:
                cmd = [
                    "find", str(self.frontend_path),
                    "-name", f"*{ext}",
                    "-not", "-path", "*/node_modules/*",
                    "-not", "-path", "*/.next/*",
                    "-exec", "grep", "-l", pattern, "{}", ";"
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                if result.returncode == 0 and result.stdout.strip():
                    files = result.stdout.strip().split('\n')
                    for file in files:
                        if file not in results_list:
                            results_list.append(file)
                            
            except subprocess.TimeoutExpired:
                logger.warning(f"Search timeout for pattern: {pattern}")
            except Exception as e:
                logger.error(f"Error searching for pattern {pattern}: {e}")
    
    def fix_debug_code(self):
        """Remove debug code from the founder dashboard."""
        logger.info("🔧 Fixing debug code...")
        
        founder_dashboard_file = self.frontend_path / "app" / "founder-dashboard" / "page.tsx"
        
        if not founder_dashboard_file.exists():
            logger.warning("Founder dashboard file not found")
            return
        
        try:
            with open(founder_dashboard_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Remove the debug div
            debug_pattern = r'\s*{/\* Debug Test \*/}.*?</div>\s*'
            content = re.sub(debug_pattern, '', content, flags=re.DOTALL)
            
            # Also remove the standalone debug div
            debug_div_pattern = r'\s*<div className="bg-red-100.*?</div>\s*'
            content = re.sub(debug_div_pattern, '', content, flags=re.DOTALL)
            
            with open(founder_dashboard_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info("✅ Debug code removed from founder dashboard")
            
        except Exception as e:
            logger.error(f"Error fixing debug code: {e}")
    
    def clear_nextjs_cache(self):
        """Clear Next.js cache files."""
        logger.info("🧹 Clearing Next.js cache...")
        
        cache_dirs = [
            self.frontend_path / ".next",
            self.frontend_path / "node_modules" / ".cache",
            self.frontend_path / ".turbo"
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir.exists():
                try:
                    subprocess.run(["rm", "-rf", str(cache_dir)], check=True)
                    logger.info(f"✅ Cleared cache: {cache_dir}")
                except subprocess.CalledProcessError as e:
                    logger.error(f"Error clearing cache {cache_dir}: {e}")
    
    def check_route_files(self) -> Dict[str, bool]:
        """Check if all required route files exist."""
        logger.info("📁 Checking route files...")
        
        required_routes = {
            "/help": self.frontend_path / "app" / "help" / "page.tsx",
            "/docs": self.frontend_path / "app" / "docs" / "page.tsx",
            "/contact": self.frontend_path / "app" / "contact" / "page.tsx",
            "/privacy": self.frontend_path / "app" / "privacy" / "page.tsx"
        }
        
        route_status = {}
        for route, file_path in required_routes.items():
            exists = file_path.exists()
            route_status[route] = exists
            status = "✅" if exists else "❌"
            logger.info(f"{status} {route}: {file_path}")
        
        return route_status
    
    def restart_dev_server(self):
        """Restart the development server."""
        logger.info("🔄 Restarting development server...")
        
        try:
            # Kill existing Next.js processes
            subprocess.run(["pkill", "-f", "next dev"], capture_output=True)
            time.sleep(2)
            
            # Start new dev server in background
            os.chdir(self.frontend_path)
            subprocess.Popen(["npm", "run", "dev"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            logger.info("✅ Development server restarted")
            
        except Exception as e:
            logger.error(f"Error restarting dev server: {e}")
    
    def generate_report(self, issues: Dict[str, List[str]]) -> str:
        """Generate a detailed report of found issues."""
        report = []
        report.append("=" * 60)
        report.append("MTBRMG ERP - Console Errors Analysis Report")
        report.append("=" * 60)
        report.append("")
        
        for category, items in issues.items():
            if items:
                report.append(f"🔍 {category.upper().replace('_', ' ')}:")
                for item in items:
                    report.append(f"  - {item}")
                report.append("")
        
        # Route status
        route_status = self.check_route_files()
        report.append("📁 ROUTE FILES STATUS:")
        for route, exists in route_status.items():
            status = "✅ EXISTS" if exists else "❌ MISSING"
            report.append(f"  {route}: {status}")
        report.append("")
        
        report.append("🔧 RECOMMENDED ACTIONS:")
        report.append("  1. Clear Next.js cache")
        report.append("  2. Remove debug code")
        report.append("  3. Restart development server")
        report.append("  4. Check browser console for remaining errors")
        report.append("")
        
        return "\n".join(report)
    
    def run_comprehensive_fix(self):
        """Run comprehensive fix for all console errors."""
        logger.info("🚀 Starting comprehensive console errors fix...")
        
        # Step 1: Analyze codebase
        issues = self.analyze_codebase()
        
        # Step 2: Generate report
        report = self.generate_report(issues)
        print(report)
        
        # Save report to file
        with open("console_errors_report.txt", "w", encoding="utf-8") as f:
            f.write(report)
        
        # Step 3: Fix debug code
        self.fix_debug_code()
        
        # Step 4: Clear cache
        self.clear_nextjs_cache()
        
        # Step 5: Restart dev server
        self.restart_dev_server()
        
        logger.info("✅ Comprehensive fix completed!")
        logger.info("📄 Report saved to: console_errors_report.txt")
        logger.info("📄 Logs saved to: console_errors_fix.log")
        
        print("\n" + "=" * 60)
        print("🎉 CONSOLE ERRORS FIX COMPLETED!")
        print("=" * 60)
        print("Next steps:")
        print("1. Wait 10-15 seconds for dev server to start")
        print("2. Open http://localhost:3001 in browser")
        print("3. Check browser console for any remaining errors")
        print("4. Navigate through the application to test all routes")

def main():
    """Main function to run the console errors fixer."""
    try:
        fixer = ConsoleErrorsFixer()
        fixer.run_comprehensive_fix()
    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise

if __name__ == "__main__":
    main()
